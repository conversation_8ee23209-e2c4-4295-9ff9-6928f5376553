# Performance Optimization Dependencies for Options Strategy Evolution Agent

# Core performance libraries
numba>=0.58.0                    # JIT compilation for numerical computations
polars>=0.20.0                   # Fast DataFrame operations
pyarrow>=14.0.0                  # Columnar data format
vectorbt>=0.25.0                 # High-performance backtesting

# Parallel processing
concurrent-futures>=3.1.1        # Backport for older Python versions (if needed)

# Scientific computing (enhanced versions)
numpy>=1.24.0                    # Numerical operations
scipy>=1.10.0                    # Scientific computing

# Memory optimization
psutil>=5.9.0                    # System and process utilities
memory-profiler>=0.61.0          # Memory usage profiling

# Async optimization
aiofiles>=23.0.0                 # Async file operations
aiohttp>=3.8.0                   # Async HTTP client

# Data serialization optimization
orjson>=3.9.0                    # Fast JSON serialization (alternative to json)
msgpack>=1.0.0                   # Binary serialization

# Optional GPU acceleration (if CUDA available)
# cupy-cuda11x>=12.0.0           # GPU-accelerated NumPy (uncomment if CUDA 11.x)
# cupy-cuda12x>=12.0.0           # GPU-accelerated NumPy (uncomment if CUDA 12.x)

# Distributed computing (for future scaling)
# dask[complete]>=2023.12.0      # Distributed computing (uncomment if needed)
# ray[default]>=2.8.0            # Scalable ML/AI (uncomment if needed)

# Profiling and monitoring
line-profiler>=4.1.0             # Line-by-line profiling
py-spy>=0.3.14                   # Sampling profiler
snakeviz>=2.2.0                  # cProfile visualization

# Development and testing
pytest>=7.4.0                    # Testing framework
pytest-asyncio>=0.21.0           # Async testing
pytest-benchmark>=4.0.0          # Performance benchmarking
hypothesis>=6.88.0                # Property-based testing

# Existing dependencies (ensure compatibility)
pandas>=2.0.0                    # Data manipulation
matplotlib>=3.7.0                # Plotting
seaborn>=0.12.0                  # Statistical visualization
plotly>=5.17.0                   # Interactive plotting
yfinance>=0.2.0                  # Financial data
ta-lib>=0.4.0                    # Technical analysis
scikit-learn>=1.3.0              # Machine learning
tensorflow>=2.13.0               # Deep learning
torch>=2.0.0                     # PyTorch
transformers>=4.35.0             # NLP models
openai>=1.0.0                    # OpenAI API
anthropic>=0.7.0                 # Anthropic API
python-dotenv>=1.0.0             # Environment variables
pyyaml>=6.0.0                    # YAML parsing
requests>=2.31.0                 # HTTP requests
asyncio>=3.4.3                   # Async programming
logging>=*******                 # Logging
pathlib>=1.0.1                   # Path handling
dataclasses>=0.8                 # Data classes
typing-extensions>=4.8.0         # Type hints
