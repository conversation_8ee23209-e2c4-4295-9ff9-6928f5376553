# Optimized Options Strategy Evolution Agent Requirements

# Core optimization libraries
polars>=0.20.0              # Fast DataFrame operations
pyarrow>=14.0.0             # Columnar data format
vectorbt>=0.25.0            # Vectorized backtesting
numba>=0.58.0               # JIT compilation

# Async and concurrency
aiofiles>=23.0.0            # Async file operations
aiohttp>=3.9.0              # Async HTTP client

# Scientific computing (optimized versions)
numpy>=1.24.0               # Numerical computing
scipy>=1.11.0               # Scientific computing

# Data processing
pandas>=2.0.0               # Data manipulation (fallback)
fastparquet>=2023.10.0      # Fast Parquet I/O

# Financial analysis
ta-lib>=0.4.0               # Technical analysis
polars-talib>=0.1.0         # Polars technical analysis

# Machine learning (optional, for advanced features)
scikit-learn>=1.3.0         # Machine learning
xgboost>=2.0.0              # Gradient boosting

# Utilities
pyyaml>=6.0.0               # YAML configuration
python-dateutil>=2.8.0     # Date utilities
pytz>=2023.3                # Timezone handling

# Monitoring and logging
psutil>=5.9.0               # System monitoring
memory-profiler>=0.61.0     # Memory profiling

# Development and testing
pytest>=7.4.0              # Testing framework
pytest-asyncio>=0.21.0     # Async testing
black>=23.0.0               # Code formatting
isort>=5.12.0               # Import sorting

# Optional GPU acceleration (if available)
cupy-cuda11x>=12.0.0        # GPU arrays (CUDA 11.x)
# cupy-cuda12x>=12.0.0      # GPU arrays (CUDA 12.x)

# Optional distributed computing
dask[complete]>=2023.10.0   # Distributed computing
ray[default]>=2.8.0         # Distributed ML

# Communication
requests>=2.31.0            # HTTP requests
smtplib-ssl>=1.0.0          # Secure email