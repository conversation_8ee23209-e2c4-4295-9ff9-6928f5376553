#!/usr/bin/env python3
"""
Performance Benchmark Script for Options Strategy Evolution Agent
Compares optimized vs unoptimized performance
"""

import time
import numpy as np
import asyncio
import logging
from typing import List, Dict
import json
from pathlib import Path
import sys

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

# Import optimized functions
try:
    from agents.options_strategy_evolution_agent import (
        calculate_sharpe_ratio_fast,
        calculate_max_drawdown_fast,
        calculate_win_rate_fast,
        calculate_expectancy_fast,
        calculate_profit_factor_fast,
        NUMBA_AVAILABLE
    )
except ImportError:
    print("Could not import optimized functions. Running basic benchmark.")
    NUMBA_AVAILABLE = False

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def calculate_sharpe_ratio_slow(returns: np.ndarray, risk_free_rate: float = 0.0) -> float:
    """Slow implementation for comparison"""
    if len(returns) == 0:
        return 0.0
    excess_returns = []
    for r in returns:
        excess_returns.append(r - risk_free_rate)
    
    if len(excess_returns) == 0:
        return 0.0
    
    mean_return = sum(excess_returns) / len(excess_returns)
    variance = sum((r - mean_return) ** 2 for r in excess_returns) / len(excess_returns)
    std_dev = variance ** 0.5
    
    if std_dev == 0:
        return 0.0
    return mean_return / std_dev

def calculate_max_drawdown_slow(returns: np.ndarray) -> float:
    """Slow implementation for comparison"""
    if len(returns) == 0:
        return 0.0
    
    cumulative = [1.0]
    for r in returns:
        cumulative.append(cumulative[-1] * (1 + r))
    
    max_drawdown = 0.0
    peak = cumulative[0]
    
    for value in cumulative:
        if value > peak:
            peak = value
        drawdown = (peak - value) / peak
        if drawdown > max_drawdown:
            max_drawdown = drawdown
    
    return -max_drawdown

def benchmark_calculations():
    """Benchmark financial calculations"""
    print("\n" + "="*60)
    print("FINANCIAL CALCULATIONS BENCHMARK")
    print("="*60)
    
    # Generate test data
    sizes = [100, 1000, 10000, 100000]
    
    for size in sizes:
        print(f"\nTesting with {size:,} data points:")
        
        # Generate random returns
        np.random.seed(42)
        returns = np.random.normal(0.001, 0.02, size)
        
        # Benchmark Sharpe ratio calculation
        if NUMBA_AVAILABLE:
            # Warm up JIT
            _ = calculate_sharpe_ratio_fast(returns[:100])
            
            start_time = time.time()
            for _ in range(10):
                result_fast = calculate_sharpe_ratio_fast(returns)
            fast_time = (time.time() - start_time) / 10
        else:
            fast_time = float('inf')
            result_fast = 0
        
        start_time = time.time()
        for _ in range(10):
            result_slow = calculate_sharpe_ratio_slow(returns)
        slow_time = (time.time() - start_time) / 10
        
        speedup = slow_time / fast_time if fast_time > 0 else float('inf')
        
        print(f"  Sharpe Ratio:")
        print(f"    Slow: {slow_time:.6f}s, Result: {result_slow:.4f}")
        if NUMBA_AVAILABLE:
            print(f"    Fast: {fast_time:.6f}s, Result: {result_fast:.4f}")
            print(f"    Speedup: {speedup:.1f}x")
        else:
            print(f"    Fast: Not available (numba not installed)")
        
        # Benchmark max drawdown calculation
        if NUMBA_AVAILABLE:
            start_time = time.time()
            for _ in range(10):
                result_fast = calculate_max_drawdown_fast(returns)
            fast_time = (time.time() - start_time) / 10
        else:
            fast_time = float('inf')
            result_fast = 0
        
        start_time = time.time()
        for _ in range(10):
            result_slow = calculate_max_drawdown_slow(returns)
        slow_time = (time.time() - start_time) / 10
        
        speedup = slow_time / fast_time if fast_time > 0 else float('inf')
        
        print(f"  Max Drawdown:")
        print(f"    Slow: {slow_time:.6f}s, Result: {result_slow:.4f}")
        if NUMBA_AVAILABLE:
            print(f"    Fast: {fast_time:.6f}s, Result: {result_fast:.4f}")
            print(f"    Speedup: {speedup:.1f}x")
        else:
            print(f"    Fast: Not available (numba not installed)")

def benchmark_parallel_processing():
    """Benchmark parallel vs sequential processing"""
    print("\n" + "="*60)
    print("PARALLEL PROCESSING BENCHMARK")
    print("="*60)
    
    def cpu_intensive_task(n: int) -> float:
        """Simulate CPU-intensive mutation task"""
        result = 0.0
        for i in range(n):
            result += np.sin(i) * np.cos(i)
        return result
    
    async def sequential_processing(tasks: List[int]) -> List[float]:
        """Sequential processing"""
        results = []
        for task in tasks:
            result = cpu_intensive_task(task)
            results.append(result)
        return results
    
    async def parallel_processing(tasks: List[int]) -> List[float]:
        """Parallel processing using asyncio"""
        loop = asyncio.get_event_loop()
        from concurrent.futures import ThreadPoolExecutor
        
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = [
                loop.run_in_executor(executor, cpu_intensive_task, task)
                for task in tasks
            ]
            results = await asyncio.gather(*futures)
        return results
    
    # Test with different workloads
    workloads = [
        [10000] * 4,    # 4 small tasks
        [50000] * 8,    # 8 medium tasks
        [100000] * 12   # 12 large tasks
    ]
    
    for i, tasks in enumerate(workloads):
        print(f"\nWorkload {i+1}: {len(tasks)} tasks of size {tasks[0]:,}")
        
        # Sequential processing
        start_time = time.time()
        seq_results = asyncio.run(sequential_processing(tasks))
        seq_time = time.time() - start_time
        
        # Parallel processing
        start_time = time.time()
        par_results = asyncio.run(parallel_processing(tasks))
        par_time = time.time() - start_time
        
        speedup = seq_time / par_time
        
        print(f"  Sequential: {seq_time:.3f}s")
        print(f"  Parallel:   {par_time:.3f}s")
        print(f"  Speedup:    {speedup:.1f}x")

def benchmark_data_structures():
    """Benchmark different data structure operations"""
    print("\n" + "="*60)
    print("DATA STRUCTURE BENCHMARK")
    print("="*60)
    
    # Test dictionary vs list operations
    sizes = [1000, 10000, 100000]
    
    for size in sizes:
        print(f"\nTesting with {size:,} items:")
        
        # Create test data
        data_dict = {f"strategy_{i}": {"value": i, "data": f"data_{i}"} for i in range(size)}
        data_list = [{"id": f"strategy_{i}", "value": i, "data": f"data_{i}"} for i in range(size)]
        
        # Benchmark dictionary lookup
        start_time = time.time()
        for i in range(0, min(1000, size), 10):
            _ = data_dict.get(f"strategy_{i}")
        dict_time = time.time() - start_time
        
        # Benchmark list search
        start_time = time.time()
        for i in range(0, min(1000, size), 10):
            target = f"strategy_{i}"
            for item in data_list:
                if item["id"] == target:
                    break
        list_time = time.time() - start_time
        
        speedup = list_time / dict_time if dict_time > 0 else float('inf')
        
        print(f"  Dictionary lookup: {dict_time:.6f}s")
        print(f"  List search:       {list_time:.6f}s")
        print(f"  Dict speedup:      {speedup:.1f}x")

def benchmark_file_operations():
    """Benchmark file I/O operations"""
    print("\n" + "="*60)
    print("FILE I/O BENCHMARK")
    print("="*60)
    
    # Create test data
    test_data = {
        f"strategy_{i}": {
            "metrics": [
                {
                    "timestamp": f"2024-01-{j+1:02d}T10:00:00",
                    "roi": np.random.random(),
                    "sharpe": np.random.random(),
                    "win_rate": np.random.random()
                }
                for j in range(10)
            ]
        }
        for i in range(1000)
    }
    
    test_file = Path("temp_benchmark_data.json")
    
    try:
        # Benchmark synchronous write
        start_time = time.time()
        with open(test_file, 'w') as f:
            json.dump(test_data, f, indent=2)
        sync_write_time = time.time() - start_time
        
        # Benchmark synchronous read
        start_time = time.time()
        with open(test_file, 'r') as f:
            loaded_data = json.load(f)
        sync_read_time = time.time() - start_time
        
        print(f"  Synchronous write: {sync_write_time:.3f}s")
        print(f"  Synchronous read:  {sync_read_time:.3f}s")
        print(f"  Data size:         {len(json.dumps(test_data))/1024/1024:.1f} MB")
        
    finally:
        # Cleanup
        if test_file.exists():
            test_file.unlink()

def main():
    """Run all benchmarks"""
    print("Options Strategy Evolution Agent - Performance Benchmark")
    print("=" * 60)
    
    if NUMBA_AVAILABLE:
        print("✓ Numba JIT compilation available")
    else:
        print("✗ Numba not available - some optimizations disabled")
    
    try:
        import polars as pl
        print("✓ Polars available for vectorized operations")
    except ImportError:
        print("✗ Polars not available")
    
    try:
        import vectorbt as vbt
        print("✓ VectorBT available for backtesting")
    except ImportError:
        print("✗ VectorBT not available")
    
    # Run benchmarks
    benchmark_calculations()
    benchmark_parallel_processing()
    benchmark_data_structures()
    benchmark_file_operations()
    
    print("\n" + "="*60)
    print("BENCHMARK COMPLETE")
    print("="*60)
    print("\nRecommendations:")
    print("1. Install numba for 10-100x speedup in calculations")
    print("2. Install polars for faster data operations")
    print("3. Install vectorbt for optimized backtesting")
    print("4. Use parallel processing for CPU-intensive tasks")
    print("5. Prefer dictionaries over lists for lookups")

if __name__ == "__main__":
    main()
