[{"strategy_id": "crossover_632b5b4c", "name": "Strategy LC_NIFTY_24500_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24500_20250718192943 and Strategy ATMLP_NIFTY_24600_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.021708447341113588, "take_profit": 0.04700175205298666}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_5aa78d9e", "name": "Strategy LS_NIFTY_25200_20250718192943", "description": "Crossover of Strategy LS_NIFTY_25200_20250718192943 and Strategy ATMLP_NIFTY_25300_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_42913bb9", "name": "Strategy LC_NIFTY_24900_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24900_20250718192943 and Strategy ATMLP_NIFTY_25000_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_365fa374", "name": "Strategy LC_NIFTY_25600_20250718192943", "description": "Crossover of Strategy LC_NIFTY_25600_20250718192943 and Strategy LC_NIFTY_24500_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_be92f3e1", "name": "Strategy LP_NIFTY_25600_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25600_20250718192943 and Strategy LC_NIFTY_24900_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.021155617389246296, "take_profit": 0.04941342320805721}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "random_mutation", "evolution_candidate"]}, {"strategy_id": "crossover_540af346", "name": "Strategy LC_NIFTY_25600_20250718192943", "description": "Crossover of Strategy LC_NIFTY_25600_20250718192943 and Strategy LP_NIFTY_24800_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_face7a65", "name": "Strategy LP_NIFTY_25900_20250718192943", "description": "Crossover of Strategy LP_NIFTY_25900_20250718192943 and Strategy ATMLP_NIFTY_24500_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_e6562cbb", "name": "Strategy LC_NIFTY_24500_20250718192943", "description": "Crossover of Strategy LC_NIFTY_24500_20250718192943 and Strategy LP_NIFTY_25700_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}, {"strategy_id": "crossover_1569e492", "name": "Strategy ATMLP_NIFTY_24500_20250718192943", "description": "Crossover of Strategy ATMLP_NIFTY_24500_20250718192943 and Strategy ATMLP_NIFTY_25300_20250718192943", "parameters": {}, "entry_conditions": [], "exit_conditions": [], "risk_management": {"max_position_size": 0.1, "stop_loss": 0.02, "take_profit": 0.05}, "timeframe": "15min", "tags": ["genetic_algorithm", "crossover", "evolution_candidate"]}]